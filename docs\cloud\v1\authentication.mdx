---
title: "Authentication"
description: "Learn how to authenticate with the Browser Use Cloud API"
icon: "lock"
mode: "wide"
---

The Browser Use Cloud API uses API keys to authenticate requests. You can obtain an API key from your [Browser Use Cloud dashboard](https://cloud.browser-use.com/settings/api-keys).

## API Keys

All API requests must include your API key in the `Authorization` header:

```bash
Authorization: Bearer YOUR_API_KEY
```

Keep your API keys secure and do not share them in publicly accessible areas such as GitHub, client-side code, or in your browser's developer tools. API keys should be stored securely in environment variables or a secure key management system.

## Example Request

Here's an example of how to include your API key in a request using Python:

```python
import requests

API_KEY = 'your_api_key_here'
BASE_URL = 'https://api.browser-use.com/api/v1'
HEADERS = {'Authorization': f'Bearer {API_KEY}'}

response = requests.get(f'{BASE_URL}/me', headers=HEADERS)
print(response.json())
```

## Verifying Authentication

You can verify that your API key is valid by making a request to the `/api/v1/me` endpoint. See the [Me endpoint documentation](/api-reference/api-v1/me) for more details.

## API Key Security

To ensure the security of your API keys:

1. **Never share your API key** in publicly accessible areas
2. **Rotate your API keys** periodically
3. **Use environment variables** to store API keys in your applications
4. **Implement proper access controls** for your API keys
5. **Monitor API key usage** for suspicious activity

If you believe your API key has been compromised, you should immediately revoke it and generate a new one from your Browser Use Cloud dashboard.
