---
title: "Pricing"
description: "Browser Use Cloud API pricing structure and cost breakdown"
icon: "dollar-sign"
mode: "wide"
---

The Browser Use Cloud API pricing consists of two components:

1. **Task Initialization Cost**: $0.01 per started task
2. **Task Step Cost**: Additional cost based on the specific model used for each step

## LLM Model Step Pricing

The following table shows the total cost per step for each available LLM model:

| Model                            | Cost per Step |
| -------------------------------- | ------------- |
| GPT-4o                           | $0.03         |
| GPT-4o mini                      | $0.01         |
| GPT-4.1                          | $0.03         |
| GPT-4.1 mini                     | $0.01         |
| O4 mini                          | $0.02         |
| O3                               | $0.03         |
| Gemini 2.0 Flash                 | $0.01         |
| Gemini 2.0 Flash Lite            | $0.01         |
| Gemini 2.5 Flash Preview (04/17) | $0.01         |
| Gemini 2.5 Flash                 | $0.01         |
| Gemini 2.5 Pro                   | $0.03         |
| Claude 3.7 Sonnet (2025-02-19)   | $0.03         |
| <PERSON> Son<PERSON> 4 (2025-05-14)     | $0.03         |
| Llama 4 Maverick 17B Instruct    | $0.01         |

## Example Cost Calculation

For example, using GPT-4.1 for a 10 step task:

- Task initialization: $0.01
- 10 steps x \$0.03 per step = \$0.30
- **Total cost: $0.31**
