---
description:
globs:
alwaysApply: true
---

## 🧠 General Guidelines for Contributing to `browser-use`

**Browser-Use** is an AI agent that autonomously interacts with the web. It takes a user-defined task, navigates web pages using Chromium via CDP, processes HTML, and repeatedly queries a language model (like `gpt-4.1-mini`) to decide the next action—until the task is completed.

---

### 🧰 Development Rules

- ✅ **Always use [`uv`](mdc:https:/github.com/astral-sh/uv) instead of `pip`**
  For deterministic and fast dependency installs.

```bash
uv venv --python 3.11
source .venv/bin/activate
uv sync
```

- ✅ **Use real model names**
  Do **not** replace `gpt-4o` with `gpt-4`. The model `gpt-4o` is a distinct release and supported.

- ✅ **Type-safe coding**
  Use **Pydantic v2 models** for all internal action schemas, task inputs/outputs, and tools I/O. This ensures robust validation and LLM-call integrity.

- ✅ **Pre-commit formatting**
  ALWAYS make sure to run pre-commit before making PRs.

---



### Notes:

- Use descriptive names and docstrings for each action.
- Prefer returning `ActionResult` with structured content to help the agent reason better.

---

## 🧠 Creating and Running an Agent

To define a task and run a browser-use agent:

```python
from browser_use import Agent, ChatOpenAI

task = "Find the CEO of OpenAI and return their name"
model = ChatOpenAI(model="gpt-4.1-mini")

agent = Agent(task=task, llm=model, tools=tools)

history = await agent.run()
```

# Never create random examples

When I ask you to implement a feature never create new files that show off that feature -> the code just gets messy. If you do anything to test it out, just do the inline code inside the terminal (if you want).
