---
title: "Available Tools"
description: "Here is the [source code](https://github.com/browser-use/browser-use/blob/main/browser_use/tools/service.py) for the default tools:"
icon: "list"
mode: "wide"
---




### Navigation & Browser Control
- **`search_google`** - Search queries in Google
- **`go_to_url`** - Navigate to URLs  
- **`go_back`** - Go back in browser history
- **`wait`** - Wait for specified seconds

### Page Interaction
- **`click_element_by_index`** - Click elements by their index
- **`input_text`** - Input text into form fields
- **`upload_file_to_element`** - Upload files to file inputs
- **`scroll`** - Scroll the page up/down
- **`scroll_to_text`** - Scroll to specific text on page
- **`send_keys`** - Send special keys (Enter, Escape, etc.)

### Tab Management  
- **`switch_tab`** - Switch between browser tabs
- **`close_tab`** - Close browser tabs

### Content Extraction
- **`extract_structured_data`** - Extract data from webpages using LLM

### Form Controls
- **`get_dropdown_options`** - Get dropdown option values
- **`select_dropdown_option`** - Select dropdown options

### File Operations
- **`write_file`** - Write content to files
- **`read_file`** - Read file contents  
- **`replace_file_str`** - Replace text in files

### Task Completion
- **`done`** - Complete the task (always available)
