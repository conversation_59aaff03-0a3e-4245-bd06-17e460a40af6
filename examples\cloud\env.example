# Browser Use Cloud API Configuration
# Copy this file to .env and fill in your values

# Required: Your Browser Use Cloud API key
# Get it from: https://cloud.browser-use.com/billing
BROWSER_USE_API_KEY=your_api_key_here

# Optional: Custom API base URL (for enterprise installations)
# BROWSER_USE_BASE_URL=https://api.browser-use.com/api/v1

# Optional: Default model preference
# BROWSER_USE_DEFAULT_MODEL=gemini-2.5-flash

# Optional: Cost limits
# BROWSER_USE_MAX_COST_PER_TASK=5.0

# Optional: Request timeout (seconds)
# BROWSER_USE_TIMEOUT=30

# Optional: Logging configuration  
# LOG_LEVEL=INFO
