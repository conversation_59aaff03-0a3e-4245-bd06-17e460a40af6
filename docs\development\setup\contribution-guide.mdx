---
title: "Contribution Guide"
description: ""
icon: "handshake"
mode: "wide"
---

## Mission

- Make developers happy
- Do more clicks than human
- Tell your computer what to do, and it gets it done.
-  Make agents faster and more reliable.


## What to work on?

- This space is moving fast. We have 10 ideas daily. Let's exchange some.
- Browse our [GitHub Issues](https://github.com/browser-use/browser-use/issues)
- Check out our most active issues on [Discord](https://discord.gg/zXJJHtJf3k) 
- Get inspiration in [`#showcase-your-work`](https://discord.com/channels/1303749220842340412/1305549200678850642) channel


## What makes a great PR?

1. Why do we need this PR?
2. Include a demo screenshot/gif 
3. Make sure the PR passes all CI tests
4. Keep your PR focused on a single feature 


## How?
1. Fork the repository
2. Create a new branch for your feature 
3. Submit a PR

We are overwhelmed with Issues. Feel free to bump your issues/PRs with comments periodically if you need faster feedback.
