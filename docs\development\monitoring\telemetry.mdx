---
title: "Telemetry"
description: "Understanding Browser Use's telemetry"
icon: "chart-mixed"
mode: "wide"
---

## Overview

Browser Use is free under the MIT license. To help us continue improving the library, we collect anonymous usage data with [PostHog](https://posthog.com) . This information helps us understand how the library is used, fix bugs more quickly, and prioritize new features.


## Opting Out

You can disable telemetry by setting the environment variable:

```bash .env
ANONYMIZED_TELEMETRY=false
```

Or in your Python code:

```python
import os
os.environ["ANONYMIZED_TELEMETRY"] = "false"
```

<Note>
  Even when enabled, telemetry has zero impact on the library's performance. Code is available in [Telemetry
  Service](https://github.com/browser-use/browser-use/tree/main/browser_use/telemetry).
</Note>
